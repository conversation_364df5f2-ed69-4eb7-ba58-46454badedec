// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Purchase.SubModules.PurchaseOrderServices.Dtos;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices.Dtos;

/// <summary>
/// PurchaseOrder表DTO
/// </summary>
public class PurchaseOrderDto : BaseDto
{

    /// <summary>
    /// 报关单号
    /// </summary>
    public string CustomsNo { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; }

    /// <summary>
    /// 订单时间
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public string OrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string OrderStatus { get; set; }

    /// <summary>
    /// 采购订单号
    /// </summary>
    public string PoNo { get; set; }

    /// <summary>
    /// 报关单号
    /// </summary>
    public string DeclarationNo { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string Supplier { get; set; }

    /// <summary>
    /// 入库时间
    /// </summary>
    public DateTime? ReceiptDate { get; set; }
    /// <summary>
    /// 总金额
    /// </summary>
     public string TotalAmount { get; set; }

    /// <summary>
    /// 总净重
    /// </summary>
    public decimal? TotalNetWeight { get; set; }

    /// <summary>
    /// 总毛重
    /// </summary>
    public decimal? TotalGrossWeight { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmDateTime { get; set; }
    /// <summary>
    /// 确认人
    /// </summary>
    public string? ConfirmUserName { get; set; }

    public CustomsDeclarationDto CustomsDeclaration { get; set; }
}
