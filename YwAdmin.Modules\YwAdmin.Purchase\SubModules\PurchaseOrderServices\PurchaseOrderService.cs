// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.PurchaseOrder.PurchaseOrderServices.Dtos;
using YwAdmin.PurchaseOrder.PurchaseOrderServices.Input;
using Mapster;
using Volo.Abp;
using YwAdmin.Purchase;
using OfficeOpenXml;
using YwAdmin.Multiplex.Contracts.helper;
using YwAdmin.SqlSugar.Entity.Import;
using YwAdmin.Purchase.SubModules.PurchaseOrderServices.Dtos;
using YwAdmin.SqlSugar.Entity.Basic;
using YwAdmin.Core.Extensions;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices;

/// <summary>
/// PurchaseOrder服务
/// </summary>
/// <param name="db">数据</param>
/// <param name="currentUser"></param>
/// <param name="userRepository">用户表</param>
/// <param name="purchaseOrderRepository">PurchaseOrder表</param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PURCHASE)]
public class PurchaseOrderService(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
    Repository<PurchaseOrderEntity> purchaseOrderRepository,
    Repository<PurchaseOrderDetail> purchaseOrderDetailRepository,
    Repository<ImportCustomsDeclaration> importCustomsDeclarationRepository
    ) : PurchaseServiceBase
{
    private readonly Repository<PurchaseOrderEntity> _purchaseOrderRepository = purchaseOrderRepository;
    private readonly Repository<PurchaseOrderDetail> _purchaseOrderDetailRepository = purchaseOrderDetailRepository;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;
    private readonly ICurrentUser _currentUser = currentUser;
    private readonly Repository<ImportCustomsDeclaration> _importCustomsDeclarationRepository = importCustomsDeclarationRepository;

    /// <summary>
    /// 查询PurchaseOrder数据（分页）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderPage(PurchaseOrderInput input)
    {
        var query = _db.Queryable<PurchaseOrderEntity>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        // if (!string.IsNullOrEmpty(input.SomeProperty))
        // {
        //     query = query.Where(x => x.SomeProperty.Contains(input.SomeProperty));
        // }

        // 应用分页
        var total = await query.CountAsync();
        var list = await query.OrderByDescending(x => x.CreateTime)
                             //.Skip((input.PageIndex - 1) * input.PageSize)
                             //.Take(input.PageSize)
                             .ToListAsync();

        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<PurchaseOrderDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });

        return new TableData
        {
            Data = resultList,
        };
    }

    /// <summary>
    /// 查询所有PurchaseOrder数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderAll(PurchaseOrderInput input)
    {
        var query = _db.Queryable<PurchaseOrderEntity>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        if (!string.IsNullOrEmpty(input.OrderNo))
        {
            query = query.Where(x => x.OrderNo.Contains(input.OrderNo));
        }
        if (!string.IsNullOrEmpty(input.OrderType))
        {
            query = query.Where(x => x.OrderType.Equals(input.OrderType));
        }
        if (!string.IsNullOrEmpty(input.OrderStatus))
        {
            query = query.Where(x => x.OrderStatus.Equals(input.OrderStatus));
        }

        if (!string.IsNullOrEmpty(input.PoNo))
        {
            query = query.Where(x => x.PoNo.Contains(input.PoNo));
        }

        if (input.StartDate != null && input.EndDate != null)
        {
            query = query.Where(x => x.OrderDate >= input.StartDate && x.OrderDate <= input.EndDate);
        }

        var list = await query.OrderByDescending(x => x.CreateTime).ToListAsync();
        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<PurchaseOrderDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  dto.ConfirmUserName = _userRepository.GetById(result.ConfirmUserId)?.Name;
                  return dto;
              });

        return new TableData
        {
            Data = resultList
        };
    }

    /// <summary>
    /// 查询所有PurchaseOrder数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderList(PurchaseOrderInput input)
    {
        var query = _db.Queryable<PurchaseOrderEntity>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        if (!string.IsNullOrEmpty(input.OrderNo))
        {
            query = query.Where(x => x.OrderNo.Contains(input.OrderNo));
        }
        if (!string.IsNullOrEmpty(input.OrderType))
        {
            query = query.Where(x => x.OrderType.Equals(input.OrderType));
        }
        if (!string.IsNullOrEmpty(input.OrderStatus))
        {
            query = query.Where(x => x.OrderStatus.Equals(input.OrderStatus));
        }

        if (!string.IsNullOrEmpty(input.PoNo))
        {
            query = query.Where(x => x.PoNo.Contains(input.PoNo));
        }

        if (input.StartDate != null && input.EndDate != null)
        {
            query = query.Where(x => x.OrderDate >= input.StartDate && x.OrderDate <= input.EndDate);
        }

        query = query.ApplyFilters(input.Filters).InputPage(input);
        var total = await query.CountAsync();
        var list = await query.ApplySort(input.SortModel).OrderByDescending(x => x.CreateTime).ToListAsync();
        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<PurchaseOrderDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  dto.ConfirmUserName = _userRepository.GetById(result.ConfirmUserId)?.Name;
                  return dto;
              });

        return new TableData
        {
            Data = resultList,
            TotalCount = total
        };
    }

    /// <summary>
    /// 新增或者更新PurchaseOrder
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    ///
    [HttpPost]
    public async Task<TableData> AddOrEditPurchaseOrder(PurchaseOrderInput input)
    {
        if (input.Id == null)
        {
            var entity = input.Adapt<PurchaseOrderEntity>();
            await _purchaseOrderRepository.InsertAsync(entity);
        }
        else
        {
            var purchaseOrderInfo = await _db.Queryable<PurchaseOrderEntity>().FirstAsync(x => x.Id == input.Id);
            if (purchaseOrderInfo != null)
            {
                // Update properties here
                await _purchaseOrderRepository.UpdateAsync(purchaseOrderInfo);
            }
            else
            {
                throw new UserFriendlyException(L["DataException"]);
            }
        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }

    /// <summary>
    /// 查询PurchaseOrder明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> GetPurchaseOrder(PurchaseOrderInput input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<PurchaseOrderEntity>().FirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<PurchaseOrderDto>();

        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        result.ConfirmUserName = _userRepository.GetById(entity.ConfirmUserId)?.Name;
        //计算订单明细的总金额，不同的币种，要这样显示：币种:金额;币种：金额;
        var orderDetails = _db.Queryable<PurchaseOrderDetail>().Where(x => x.OrderNo == result.OrderNo).ToList();

        // 按币种分组计算总金额
        var currencyTotals = orderDetails
            .Where(x => x.Amount.HasValue && !string.IsNullOrEmpty(x.Currency))
            .GroupBy(x => x.Currency)
            .Select(g => new { Currency = g.Key, TotalAmount = g.Sum(x => x.Amount.Value) })
            .ToList();

        // 格式化为 "币种:金额;币种:金额;" 的形式
        result.TotalAmount = string.Join(";", currencyTotals.Select(ct => $"{ct.Currency}:{ct.TotalAmount:F2}"));
        result.TotalGrossWeight = orderDetails.Sum(x => x.GrossWeight);
        result.TotalNetWeight = orderDetails.Sum(x => x.NetWeight);

        //查询报关单信息
        var customsDeclaration = await _db.Queryable<ImportCustomsDeclaration>().FirstAsync(x => x.OrderNo == result.OrderNo);
        result.CustomsDeclaration = customsDeclaration.Adapt<CustomsDeclarationDto>();
        return new TableData
        {
            Data = result,
        };
    }

    /// <summary>
    /// 下载Excel导入模版
    /// </summary>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> DownLoadExcel()
    {
        try
        {
            // 创建文件流并使用 using 语句自动管理资源
            using (var fileStream = new MemoryStream())
            {
                using (var package = new ExcelPackage(fileStream))
                {
                    // 创建单个工作表
                    var worksheet = package.Workbook.Worksheets.Add("采购订单导入");

                    // 设置主表区域标题
                    worksheet.Cells[1, 1].Value = "【订单信息】";
                    worksheet.Cells[1, 1, 1, 9].Merge = true;
                    worksheet.Cells[1, 1].Style.Font.Bold = true;
                    worksheet.Cells[1, 1].Style.Font.Size = 12;
                    worksheet.Cells[1, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 1].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightBlue);

                    // 设置主表表头
                    var mainHeaders = new string[]
                    {
        "采购订单号(必填)",
        "报关单号",
        "供应商"
                    };

                    // 设置主表表头
                    for (int i = 0; i < mainHeaders.Length; i++)
                    {
                        worksheet.Cells[2, i + 1].Value = mainHeaders[i];
                        worksheet.Cells[2, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[2, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[2, i + 1].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightGray);
                        worksheet.Cells[2, i + 1].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[2, i + 1].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[2, i + 1].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[2, i + 1].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    }

                    // 添加主表数据行（示例数据）
                    //worksheet.Cells[3, 1].Value = DateTime.Now.ToString("yyyy-MM-dd");
                    //worksheet.Cells[3, 2].Value = "PO-2023001";
                    //worksheet.Cells[3, 3].Value = "DC-2023001";
                    //worksheet.Cells[3, 4].Value = "SUP001";

                    // 为主表数据行添加边框
                    for (int i = 0; i < mainHeaders.Length; i++)
                    {
                        worksheet.Cells[3, i + 1].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[3, i + 1].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[3, i + 1].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[3, i + 1].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    }

                    // 空行分隔
                    worksheet.Cells[5, 1].Value = "【明细信息】";
                    worksheet.Cells[5, 1, 5, 9].Merge = true;
                    worksheet.Cells[5, 1].Style.Font.Bold = true;
                    worksheet.Cells[5, 1].Style.Font.Size = 12;
                    worksheet.Cells[5, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[5, 1].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightBlue);

                    // 设置明细列头
                    var detailHeaders = new string[]
                    {
        "序号",
        "原材料料号(必填)",
        "数量",
        "件数",
        "毛重",
        "净重",
        "体积",
        "单价",
        "金额",
        "币种"
                    };

                    // 设置明细列
                    for (int i = 0; i < detailHeaders.Length; i++)
                    {
                        worksheet.Cells[6, i + 1].Value = detailHeaders[i];
                        worksheet.Cells[6, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[6, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[6, i + 1].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightGray);
                        worksheet.Cells[6, i + 1].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[6, i + 1].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[6, i + 1].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        worksheet.Cells[6, i + 1].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    }

                    // 添加明细示例数据行
                    for (int row = 7; row <= 9; row++)
                    {
                        worksheet.Cells[row, 1].Value = row - 6; // 序号

                        // 为明细数据行添加边框
                        for (int i = 0; i < detailHeaders.Length; i++)
                        {
                            worksheet.Cells[row, i + 1].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                            worksheet.Cells[row, i + 1].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                            worksheet.Cells[row, i + 1].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                            worksheet.Cells[row, i + 1].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        }
                    }

                    // 自动调整列宽
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // 保存文件内容
                    package.Save();
                }

                fileStream.Position = 0;  // 重置流位置
                var fileData = fileStream.ToArray();
                // 返回 TableData
                return new TableData
                {
                    Data = new
                    {
                        FileContent = fileData, // 返回字节数组
                        FileName = "采购导入模版.xlsx",
                        MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // 记录异常并返回错误信息
            return new TableData
            {
                Code = 500,
                Message = "Failed to generate Excel template: " + ex.Message
            };
        }
    }

    /// <summary>
    /// 根据数据导入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<TableData> ImportByExcel([FromForm] PurchaseOrderExcel input)
    {
        var resultList = new PurchaseOrderExcelDto();
        var main = new PurchaseOrderDto();
        var detail = new List<PurchaseOrderDetailExcelDto>();
        using (var package = new ExcelPackage(input.InputStream.OpenReadStream()))
        {
            ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
            main = GetSheet(worksheet);

            // 如果只有一个工作表，从同一个工作表读取明细信息（从第6行开始）
            detail = GetDetailSheet(worksheet);
        }
        resultList.PurchaseOrder = main;
        resultList.PurchaseOrderDetail = detail;
        return new TableData { Data = resultList };
    }

    private List<PurchaseOrderDetailExcelDto> GetDetailSheet(ExcelWorksheet worksheet)
    {
        var itemLotExcelList = new List<PurchaseOrderDetailExcelDto>();

        try
        {
            // 根据模板结构，明细信息表头在第6行，数据从第7行开始
            var columnMapping = new Dictionary<string, int>();
            var columnNames = new List<string> { "序号", "原材料料号(必填)", "数量", "件数", "毛重", "净重", "体积", "单价", "金额", "币种" };
            var headerRow = 6; // 明细表头在第6行

            // 读取表头，建立列名到列索引的映射
            for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
            {
                string? columnName = worksheet.Cells[headerRow, col].Text?.Trim();
                if (!string.IsNullOrEmpty(columnName) && columnNames.Contains(columnName) && !columnMapping.ContainsKey(columnName))
                {
                    columnMapping[columnName] = col;
                }
            }

            // 从第7行开始读取数据
            for (var row = headerRow + 1; row <= worksheet.Dimension.End.Row; row++)
            {
                var itemExcel = new PurchaseOrderDetailExcelDto();
                var strErrorMsg = string.Empty;
                bool hasData = false;

                // 读取序号
                if (columnMapping.TryGetValue("序号", out int seqCol))
                {
                    var seqValue = worksheet.Cells[row, seqCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(seqValue) && int.TryParse(seqValue, out int seq))
                    {
                        itemExcel.Seq = seq;
                        hasData = true;
                    }
                }

                // 读取原材料料号（必填）
                if (columnMapping.TryGetValue("原材料料号(必填)", out int mCodeCol))
                {
                    var mCodeValue = worksheet.Cells[row, mCodeCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(mCodeValue))
                    {
                        itemExcel.MCode = mCodeValue;
                        //如果系统中没有该料号，不允许导入
                        var material = _db.Queryable<Material>().First(x => x.MCode == itemExcel.MCode);
                        if (material != null)
                        {
                            hasData = true;
                            itemExcel.ItemCode = material.ProductCode;
                            itemExcel.ItemName = material.ProductName;
                            itemExcel.SpecModel = material.SpecModel;
                            itemExcel.Unit = material.Unit;
                        }
                        else
                        {
                            strErrorMsg += "原材料料号在系统中不存在,";
                        }
                    }
                    else if (hasData) // 如果有其他数据但原材料料号为空，记录错误
                    {
                        strErrorMsg += "原材料料号不能为空,";
                    }
                }

                // 读取数量
                if (columnMapping.TryGetValue("数量", out int qtyCol))
                {
                    var qtyValue = worksheet.Cells[row, qtyCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(qtyValue) && decimal.TryParse(qtyValue, out decimal qty))
                    {
                        itemExcel.Qty = qty;
                        hasData = true;
                    }
                }

                // 读取件数
                if (columnMapping.TryGetValue("件数", out int packageCountCol))
                {
                    var packageCountValue = worksheet.Cells[row, packageCountCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(packageCountValue) && int.TryParse(packageCountValue, out int packageCount))
                    {
                        itemExcel.PackageCount = packageCount;
                    }
                }

                // 读取毛重
                if (columnMapping.TryGetValue("毛重", out int grossWeightCol))
                {
                    var grossWeightValue = worksheet.Cells[row, grossWeightCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(grossWeightValue) && decimal.TryParse(grossWeightValue, out decimal grossWeight))
                    {
                        itemExcel.GrossWeight = grossWeight;
                    }
                }

                // 读取净重
                if (columnMapping.TryGetValue("净重", out int netWeightCol))
                {
                    var netWeightValue = worksheet.Cells[row, netWeightCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(netWeightValue) && decimal.TryParse(netWeightValue, out decimal netWeight))
                    {
                        itemExcel.NetWeight = netWeight;
                    }
                }

                // 读取体积
                if (columnMapping.TryGetValue("体积", out int volumeCol))
                {
                    var volumeValue = worksheet.Cells[row, volumeCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(volumeValue) && decimal.TryParse(volumeValue, out decimal volume))
                    {
                        itemExcel.Volume = volume;
                    }
                }

                // 读取单价
                if (columnMapping.TryGetValue("单价", out int unitPriceCol))
                {
                    var unitPriceValue = worksheet.Cells[row, unitPriceCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(unitPriceValue) && decimal.TryParse(unitPriceValue, out decimal unitPrice))
                    {
                        itemExcel.UnitPrice = unitPrice;
                    }
                }

                // 读取金额
                if (columnMapping.TryGetValue("金额", out int amountCol))
                {
                    var amountValue = worksheet.Cells[row, amountCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(amountValue) && decimal.TryParse(amountValue, out decimal amount))
                    {
                        itemExcel.Amount = amount;
                    }
                }

                // 读取币种
                if (columnMapping.TryGetValue("币种", out int currencyCol))
                {
                    var currencyValue = worksheet.Cells[row, currencyCol].Text?.Trim();
                    if (!string.IsNullOrEmpty(currencyValue))
                    {
                        itemExcel.Currency = currencyValue;
                    }
                }

                // 只有当行中有数据时才添加到列表
                if (hasData)
                {
                    itemExcel.ErrorMsg = strErrorMsg.Length > 0 ? strErrorMsg.TrimEnd(',') : string.Empty;
                    itemExcel.HasErrorMsg = !string.IsNullOrEmpty(itemExcel.ErrorMsg);
                    itemLotExcelList.Add(itemExcel);
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            // 可以记录日志或抛出更具体的异常
            throw new UserFriendlyException($"读取Excel明细信息时发生错误: {ex.Message}");
        }

        return itemLotExcelList;
    }

    private PurchaseOrderDto GetSheet(ExcelWorksheet worksheet)
    {
        var purchaseOrderDto = new PurchaseOrderDto();

        try
        {
            // 创建列名映射，用于动态获取列位置
            var columnMapping = new Dictionary<string, int>();
            var headerRow = 2; // 表头在第2行
            var dataRow = 3;   // 数据在第3行

            // 读取表头，建立列名到列索引的映射
            for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
            {
                var headerValue = worksheet.Cells[headerRow, col].Text?.Trim();
                if (!string.IsNullOrEmpty(headerValue))
                {
                    columnMapping[headerValue] = col;
                }
            }

            // 读取订单时间
            //if (columnMapping.TryGetValue("订单时间(必填)", out int orderDateCol))
            //{
            //    var orderDateValue = worksheet.Cells[dataRow, orderDateCol].Text?.Trim();
            //    if (!string.IsNullOrEmpty(orderDateValue))
            //    {
            //        if (DateTime.TryParse(orderDateValue, out DateTime orderDate))
            //        {
            //            purchaseOrderDto.OrderDate = orderDate;
            //        }+
            //    }
            //}

            // 读取采购订单号
            if (columnMapping.TryGetValue("采购订单号(必填)", out int poNoCol))
            {
                var poNoValue = worksheet.Cells[dataRow, poNoCol].Text?.Trim();
                if (!string.IsNullOrEmpty(poNoValue))
                {
                    purchaseOrderDto.PoNo = poNoValue;
                    // 如果没有单独的订单号，可以使用采购订单号作为订单号
                    if (string.IsNullOrEmpty(purchaseOrderDto.OrderNo))
                    {
                        purchaseOrderDto.OrderNo = poNoValue;
                    }
                }
            }

            // 读取报关单号
            if (columnMapping.TryGetValue("报关单号", out int declarationNoCol))
            {
                var declarationNoValue = worksheet.Cells[dataRow, declarationNoCol].Text?.Trim();
                if (!string.IsNullOrEmpty(declarationNoValue))
                {
                    purchaseOrderDto.DeclarationNo = declarationNoValue;
                }
            }

            // 读取供应商
            if (columnMapping.TryGetValue("供应商", out int SupplierCol))
            {
                var SupplierValue = worksheet.Cells[dataRow, SupplierCol].Text?.Trim();
                if (!string.IsNullOrEmpty(SupplierValue))
                {
                    purchaseOrderDto.Supplier = SupplierValue;
                }
            }

            // 设置默认值
            if (string.IsNullOrEmpty(purchaseOrderDto.OrderType))
            {
                purchaseOrderDto.OrderType = "采购订单"; // 默认订单类型
            }

            if (string.IsNullOrEmpty(purchaseOrderDto.OrderStatus))
            {
                purchaseOrderDto.OrderStatus = "待处理"; // 默认订单状态
            }
        }
        catch (Exception ex)
        {
            // 可以记录日志或抛出更具体的异常
            throw new UserFriendlyException($"读取Excel订单信息时发生错误: {ex.Message}");
        }

        return purchaseOrderDto;
    }

    /// <summary>
    /// 保存导入的数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> InsertPurchase(PurchaseOrderExcelDto input)
    {
        try
        {
            //生成OrderNo
            string order = "";
            var strDate = "";
            if (input.OrderType == "import")
            {
                strDate = "IMP";
                //新增报关单信息
                var customs = new ImportCustomsDeclaration();
                //获取到数据字典当前公司信息、
                var customerCode = _db.Queryable<DictionaryCode>().First(x => x.CodeType == "CustomerApi" && x.Value == "CustomerName");
                if (customerCode != null && !string.IsNullOrEmpty(customerCode.Text))
                {
                    //根据Text获取到公司信息
                    var customInfo = _db.Queryable<CompanyInfo>().First(x => x.CompanyCode == customerCode.Text);
                    if (customInfo != null)
                    {
                        customs.ImportCompanyCode = customInfo.CompanyCode;
                        customs.ImportTaxCode = customInfo.TaxCode;
                        customs.ImportCompanyName = customInfo.CompanyName;
                        customs.ImportPostalCode = customInfo.PostalCode;
                        customs.ImportAddress = customInfo.Address;
                        customs.ImportPhone = customInfo.Phone;
                        customs.ImportContactPerson = customInfo.ContactName;
                        customs.ImportContactPhone = customInfo.ContactPhone;
                        customs.ImportEmail = customInfo.Email;
                        customs.ImportTypeCode = customInfo.TypeCode;
                        customs.ImportCustomsAuthority = customInfo.CustomsAuthority;
                        customs.ImportGoodsClassificationCode = customInfo.GoodsClassificationCode;
                        customs.ImportPersonOrgClassification = customInfo.PersonOrgType;
                        customs.ImportDeclarationDeptCode = customInfo.DeclarationDeptCode;
                        customs.ImportTransportModeCode = customInfo.TransportModeCode;
                        await _importCustomsDeclarationRepository.InsertAsync(customs);
                    }

                }
            }
            else
            {
                strDate = "LOC";
            }
            strDate = strDate + DateTime.Now.ToString("yyyyMM");
            var maxId = await _db.Queryable<PurchaseOrderEntity>().Where(x => x.OrderNo.Contains(strDate)).OrderByDescending(x => x.OrderNo).Select(x => x.OrderNo).FirstAsync();
            if (string.IsNullOrEmpty(maxId))
            {
                order = strDate + "0001";
            }
            else
            {
                string fgCodeEnd = maxId.Substring(maxId.Length - 4);
                if (int.TryParse(fgCodeEnd, out int lastTwoDigits))
                {
                    int newFGCode = lastTwoDigits + 1; // 加 1
                    order = strDate + newFGCode.ToString().PadLeft(4, '0');
                }
            }
            var purchaseOrder = new PurchaseOrderEntity();
            purchaseOrder.OrderNo = order;
            purchaseOrder.OrderDate = DateTime.Now;
            purchaseOrder.OrderType = input.OrderType;
            purchaseOrder.OrderStatus = "new";
            purchaseOrder.PoNo = input.PurchaseOrder.PoNo;
            purchaseOrder.DeclarationNo = input.PurchaseOrder.DeclarationNo;
            purchaseOrder.Supplier = input.PurchaseOrder.Supplier;
            purchaseOrder.ReceiptDate = input.PurchaseOrder.ReceiptDate;
            await _purchaseOrderRepository.InsertReturnIdAsync(purchaseOrder);

            foreach (var item in input.PurchaseOrderDetail)
            {
                var purchaseOrderDetail = new PurchaseOrderDetail();
                purchaseOrderDetail.OrderNo = order;

                purchaseOrderDetail.Seq = item.Seq;
                purchaseOrderDetail.MCode = item.MCode;
                purchaseOrderDetail.ItemCode = item.ItemCode;
                purchaseOrderDetail.ItemName = item.ItemName;
                purchaseOrderDetail.SpecModel = item.SpecModel;
                purchaseOrderDetail.Unit = item.Unit;
                purchaseOrderDetail.Qty = item.Qty;
                purchaseOrderDetail.PackageCount = item.PackageCount;
                purchaseOrderDetail.GrossWeight = item.GrossWeight;
                purchaseOrderDetail.NetWeight = item.NetWeight;
                purchaseOrderDetail.Volume = item.Volume;
                purchaseOrderDetail.UnitPrice = item.UnitPrice;
                purchaseOrderDetail.Amount = item.Amount;
                purchaseOrderDetail.Currency = item.Currency;
                await _purchaseOrderDetailRepository.InsertReturnIdAsync(purchaseOrderDetail);
            }
            return new TableData { Message = L["UpdateSuccessful"] };
        }
        catch (Exception)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
    }

    /// <summary>
    /// 根据ID删除采购和对应的明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> DeletePurchase(PurchaseOrderInput input)
    {
        try
        {
            //根据id查询，然后进行删除
            var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(input.Id);
            if (purchaseOrder != null)
            {
                if (purchaseOrder.OrderStatus != "new")
                {
                    throw new UserFriendlyException(L["OrderNotDel"]);
                }
                await _purchaseOrderRepository.DeleteAsync(purchaseOrder);
                //在进行删除明细数据
                await _purchaseOrderDetailRepository.DeleteAsync(x => x.OrderNo == purchaseOrder.OrderNo);
                return new TableData { Message = L["DeleteSuccessful"] };
            }
            else
            {
                return new TableData { Message = L["DeleteFailed"] };
            }
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }
    }

    /// <summary>
    /// 新增接口，确认接口，更新为确认状态：confirm
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> ConfirmPurchase(PurchaseOrderInput input)
    {
        try
        {
            var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(input.Id);
            if (purchaseOrder != null)
            {
                purchaseOrder.OrderStatus = "confirm";
                purchaseOrder.ConfirmUserId = _currentUser.GetUserId();
                purchaseOrder.ConfirmDateTime = DateTime.Now;
                await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
                return new TableData { Message = L["UpdateSuccessful"] };
            }
            else
            {
                return new TableData { Message = L["UpdateFailed"] };
            }
        }
        catch (Exception)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
    }

    /// <summary>
    /// 取消确认接口
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> CancelPurchase(PurchaseOrderInput input)
    {
        try
        {
            var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(input.Id);
            if (purchaseOrder != null)
            {
                purchaseOrder.OrderStatus = "new";
                purchaseOrder.ConfirmUserId = null;
                purchaseOrder.ConfirmDateTime = null;
                await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
                return new TableData { Message = L["UpdateSuccessful"] };
            }
            else
            {
                return new TableData { Message = L["UpdateFailed"] };
            }
        }
        catch (Exception)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
    }

    /// <summary>
    /// 国外采购-报关单信息修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> EditCompany(AddCustomsDeclarationDto input)
    {
        try
        {
            var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(input.Id);
            if (purchaseOrder == null)
            {
                return new TableData { Message = L["UpdateFailed"] };
            }

            purchaseOrder.DeclarationNo = input.DeclarationNo;

            await _purchaseOrderRepository.UpdateAsync(purchaseOrder);

            //根据业务编号查看是否有对应报关信息
            var customsInfo = await _db.Queryable<ImportCustomsDeclaration>().FirstAsync(x => x.OrderNo == purchaseOrder.OrderNo);
            //没有则进行新增
            if (customsInfo != null)
            {
                var id = customsInfo.Id;
                input.Adapt(customsInfo);
                customsInfo.Id = id;
                await _importCustomsDeclarationRepository.UpdateAsync(customsInfo);
            }
            else
            {
                var dto = input.Adapt(customsInfo);
                await _importCustomsDeclarationRepository.InsertAsync(dto);
            }
            return new TableData { };

        }
        catch (Exception exc)
        {

            throw new UserFriendlyException(exc.Message);
        }
    }



    [HttpPost]
    public async Task<TableData> DownLoadCustomsExcel(CustomsExcelInput input)
    {
        try
        {
            // 验证输入参数
            if (string.IsNullOrEmpty(input.OrderNo))
            {
                return new TableData
                {
                    Code = 400,
                    Message = L["OrderNoEmpty"]
                };
            }

            // 根据订单号获取报关单数据
            var customsData = await _importCustomsDeclarationRepository.GetFirstAsync(x => x.OrderNo == input.OrderNo);
            if (customsData == null)
            {
                return new TableData
                {
                    Code = 404,
                    Message = L["CustomsDataNotFound"]
                };
            }

            // 创建文件流并使用 using 语句自动管理资源
            using (var fileStream = new MemoryStream())
            {
                using (var package = new ExcelPackage(fileStream))
                {
                    // 创建单个工作表
                    var worksheet = package.Workbook.Worksheets.Add(L["CustomsDeclarationInfo"]);

                    int currentRow = 1;

                    // 1. 进口公司信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["ImportCompanyInfo"], new[]
                    {
                    new { Label = L["CompanyCode"], Value = customsData.ImportCompanyCode ?? "" },
                    new { Label = L["CompanyTaxCode"], Value = customsData.ImportTaxCode ?? "" },
                    new { Label = L["CompanyName"], Value = customsData.ImportCompanyName ?? "" },
                    new { Label = L["PostalCode"], Value = customsData.ImportPostalCode ?? "" },
                    new { Label = L["Address"], Value = customsData.ImportAddress ?? "" },
                    new { Label = L["PhoneNumber"], Value = customsData.ImportPhone ?? "" },
                    new { Label = L["ContactPerson"], Value = customsData.ImportContactPerson ?? "" },
                    new { Label = L["ContactPhone"], Value = customsData.ImportContactPhone ?? "" },
                    new { Label = L["Email"], Value = customsData.ImportEmail ?? "" },
                    new { Label = L["TypeCode"], Value = customsData.ImportTypeCode ?? "" },
                    new { Label = L["CustomsAuthority"], Value = customsData.ImportCustomsAuthority ?? "" },
                    new { Label = L["GoodsClassificationCode"], Value = customsData.ImportGoodsClassificationCode ?? "" },
                    new { Label = L["PersonOrgClassification"], Value = customsData.ImportPersonOrgClassification ?? "" },
                    new { Label = L["DeclarationDeptCode"], Value = customsData.ImportDeclarationDeptCode ?? "" },
                    new { Label = L["TransportModeCode"], Value = customsData.ImportTransportModeCode ?? "" }
                });

                    currentRow += 2; // 空行分隔

                    // 2. 出口公司信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["ExportCompanyInfo"], new[]
                    {
                    new { Label = L["CompanyCode"], Value = customsData.ExportCompanyCode ?? "" },
                    new { Label = L["CompanyTaxCode"], Value = customsData.ExportTaxCode ?? "" },
                    new { Label = L["CompanyName"], Value = customsData.ExportCompanyName ?? "" },
                    new { Label = L["PostalCode"], Value = customsData.ExportPostalCode ?? "" },
                    new { Label = L["Address"], Value = customsData.ExportAddress ?? "" },
                    new { Label = L["CountryCode"], Value = customsData.ExportCountryCode ?? "" }
                });

                    currentRow += 2; // 空行分隔

                    // 3. 提单信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["BillInfo"], new[]
                    {
                    new { Label = L["BillNo"], Value = customsData.BillNo ?? "" },
                    new { Label = L["ATD"], Value = customsData.ATD?.ToString("yyyy-MM-dd") ?? "" },
                    new { Label = L["PackageCount"], Value = customsData.PackageCount?.ToString() ?? "" },
                    new { Label = L["GrossWeight"], Value = customsData.GrossWeight?.ToString() ?? "" },
                    new { Label = L["WarehouseLocationCode"], Value = customsData.WarehouseLocationCode ?? "" },
                    new { Label = L["TransportMode"], Value = customsData.TransportMode ?? "" },
                    new { Label = L["ATA"], Value = customsData.ATA?.ToString("yyyy-MM-dd") ?? "" },
                    new { Label = L["DischargePort"], Value = customsData.DischargePort ?? "" },
                    new { Label = L["LoadingPort"], Value = customsData.LoadingPort ?? "" },
                    new { Label = L["ContainerCount"], Value = customsData.ContainerCount?.ToString() ?? "" }
                });

                    currentRow += 2; // 空行分隔

                    // 4. 许可证信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["LicenseInfo"], new[]
                    {
                    new { Label = L["LicenseContractNo"], Value = customsData.LicenseContractNo ?? "" },
                    new { Label = L["LicenseContractDate"], Value = customsData.LicenseContractDate?.ToString("yyyy-MM-dd") ?? "" },
                    new { Label = L["LicenseExpiryDate"], Value = customsData.LicenseExpiryDate?.ToString("yyyy-MM-dd") ?? "" },
                    new { Label = L["LicenseImportNo"], Value = customsData.LicenseImportNo ?? "" }
                });

                    currentRow += 2; // 空行分隔

                    // 5. 发票信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["InvoiceInfo"], new[]
                    {
                         new { Label = L["InvoiceType"], Value = customsData.InvoiceType ?? "" },
                         new { Label = L["InvoiceNo"], Value = customsData.InvoiceNo ?? "" },
                         new { Label = L["InvoiceDate"], Value = customsData.InvoiceDate?.ToString("yyyy-MM-dd") ?? "" },
                         new { Label = L["PaymentMethod"], Value = customsData.PaymentMethod ?? "" },
                         new { Label = L["InvoiceTypeCode"], Value = customsData.InvoiceTypeCode ?? "" },
                         new { Label = L["InvoiceAmount"], Value = customsData.InvoiceAmount?.ToString() ?? "" },
                         new { Label = L["DeliveryTerms"], Value = customsData.DeliveryTerms ?? "" },
                         new { Label = L["Currency"], Value = customsData.Currency ?? "" },
                         new { Label = L["CustomsValueCode"], Value = customsData.CustomsValueCode ?? "" },
                         new { Label = L["Freight"], Value = customsData.Freight?.ToString() ?? "" },
                         new { Label = L["Insurance"], Value = customsData.Insurance?.ToString() ?? "" }
                    });

                    currentRow += 2; // 空行分隔

                    // 6. 税务信息
                    currentRow = CreateCompanySection(worksheet, currentRow, L["TaxInfo"], new[]
                    {
                        new { Label = L["TaxDueDateCode"], Value = customsData.TaxDueDateCode ?? "" }
                    });

                    var detail = _db.Queryable<PurchaseOrderDetail>().Where(x=>x.OrderNo  == input.OrderNo);
                    //获取到明细数据
                    // 7. 明细信息
                    //列的信息是：序号、原材料料号、商品编码、商品名称、规格型号、单位、数量、件数、毛重、净重、体积、单价、金额、币种
                    

                    // 自动调整列宽
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // 保存文件内容
                    package.Save();
                }

                fileStream.Position = 0;  // 重置流位置
                var fileData = fileStream.ToArray();

                // 返回 TableData
                return new TableData
                {
                    Data = new
                    {
                        fileContent = Convert.ToBase64String(fileData), // 转换为Base64字符串
                        fileName = $"报关单信息_{input.OrderNo}_{DateTime.Now:yyyyMMdd}.xlsx",
                        MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // 记录异常并返回错误信息
            return new TableData
            {
                Code = 500,
                Message = L["CustomsExcelGenerateError"] + ": " + ex.Message
            };
        }
    }

    // 辅助方法：创建信息区域
    private int CreateCompanySection(ExcelWorksheet worksheet, int startRow, string title, dynamic[] fields)
    {
        // 设置标题
        worksheet.Cells[startRow, 1].Value = title;
        worksheet.Cells[startRow, 1, startRow, 4].Merge = true;
        worksheet.Cells[startRow, 1].Style.Font.Bold = true;
        worksheet.Cells[startRow, 1].Style.Font.Size = 12;
        worksheet.Cells[startRow, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
        worksheet.Cells[startRow, 1].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightBlue);

        int currentRow = startRow + 1;
        int col = 1;

        // 每行显示2个字段
        for (int i = 0; i < fields.Length; i++)
        {
            // 标签
            worksheet.Cells[currentRow, col].Value = fields[i].Label;
            worksheet.Cells[currentRow, col].Style.Font.Bold = true;
            worksheet.Cells[currentRow, col].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            worksheet.Cells[currentRow, col].Style.Fill.BackgroundColor.SetColor(SixLabors.ImageSharp.Color.LightGray);
            SetCellBorder(worksheet.Cells[currentRow, col]);

            // 值
            worksheet.Cells[currentRow, col + 1].Value = fields[i].Value ?? "";
            SetCellBorder(worksheet.Cells[currentRow, col + 1]);

            col += 2;

            // 每行2个字段，换行
            if (col > 4 || i == fields.Length - 1)
            {
                currentRow++;
                col = 1;
            }
        }

        return currentRow;
    }

    // 辅助方法：设置单元格边框
    private void SetCellBorder(ExcelRange cell)
    {
        cell.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        cell.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        cell.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        cell.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
    }


}