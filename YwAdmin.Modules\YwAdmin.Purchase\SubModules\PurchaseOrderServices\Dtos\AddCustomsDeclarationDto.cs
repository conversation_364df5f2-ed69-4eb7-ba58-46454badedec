﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Purchase.SubModules.PurchaseOrderServices.Dtos;
public class AddCustomsDeclarationDto : BaseDto
{
    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; }

    // ===== 出口公司信息 =====

    /// <summary>
    /// 出口公司代码
    /// </summary>
    public string ExportCompanyCode { get; set; }

    /// <summary>
    /// 出口公司名称
    /// </summary>
    public string ExportCompanyName { get; set; }

     /// <summary>
    /// 公司税号
    /// </summary>
    public string ExportTaxCode { get; set; }

    /// <summary>
    /// 出口公司邮政编码
    /// </summary>
    public string ExportPostalCode { get; set; }

    /// <summary>
    /// 出口公司地址
    /// </summary>
    public string ExportAddress { get; set; }

    /// <summary>
    /// 出口公司国家代码
    /// </summary>
    public string ExportCountryCode { get; set; }

    // ===== 进口公司信息 =====

    /// <summary>
    /// 进口公司代码
    /// </summary>
    public string ImportCompanyCode { get; set; }


    /// <summary>
    /// 进口公司税号
    /// </summary>
    public string ImportTaxCode { get; set; }

    /// <summary>
    /// 进口公司名称
    /// </summary>
    public string ImportCompanyName { get; set; }

    /// <summary>
    /// 进口公司邮政编码
    /// </summary>
    public string ImportPostalCode { get; set; }

    /// <summary>
    /// 进口公司地址
    /// </summary>
    public string ImportAddress { get; set; }

    /// <summary>
    /// 进口公司电话号码
    /// </summary>
    public string ImportPhone { get; set; }

    /// <summary>
    /// 进口公司联系人名称
    /// </summary>
    public string ImportContactPerson { get; set; }

    /// <summary>
    /// 进口公司联系人电话号码
    /// </summary>
    public string ImportContactPhone { get; set; }

    /// <summary>
    /// 进口公司邮件
    /// </summary>
    public string ImportEmail { get; set; }

    /// <summary>
    /// 进口公司类型代码（1:生产企业,2:贸易公司,3:其他）
    /// </summary>
    public string ImportTypeCode { get; set; }

    /// <summary>
    /// 进口公司海关当局
    /// </summary>
    public string ImportCustomsAuthority { get; set; }

    /// <summary>
    /// 进口公司货物分类代码
    /// </summary>
    public string ImportGoodsClassificationCode { get; set; }

    /// <summary>
    /// 进口公司个人/组织分类（person/organization）
    /// </summary>
    public string ImportPersonOrgClassification { get; set; }

    /// <summary>
    /// 进口公司申报单处理部门代码
    /// </summary>
    public string ImportDeclarationDeptCode { get; set; }

    /// <summary>
    /// 进口公司运输方式代码
    /// </summary>
    public string ImportTransportModeCode { get; set; }

    // ===== 提单信息 =====

    /// <summary>
    /// 提单号
    /// </summary>
    public string BillNo { get; set; }

    /// <summary>
    /// 开航日期（ATD）
    /// </summary>
    public DateTime? ATD { get; set; }

    /// <summary>
    /// 提单件数
    /// </summary>
    public int? PackageCount { get; set; }

    /// <summary>
    /// 提单货物毛重(kg)
    /// </summary>
    public decimal? GrossWeight { get; set; }

    /// <summary>
    /// 提单预期仓库位置代码
    /// </summary>
    public string WarehouseLocationCode { get; set; }

    /// <summary>
    /// 提单运输方式（sea/air/land/rail）
    /// </summary>
    public string TransportMode { get; set; }

    /// <summary>
    /// 到货日期（ATA）
    /// </summary>
    public DateTime? ATA { get; set; }

    /// <summary>
    /// 卸货港
    /// </summary>
    public string DischargePort { get; set; }

    /// <summary>
    /// 装货港
    /// </summary>
    public string LoadingPort { get; set; }

    /// <summary>
    /// 提单集装箱数量
    /// </summary>
    public int? ContainerCount { get; set; }

    // ===== 许可证信息 =====

    /// <summary>
    /// 加工合同单号（可为空）
    /// </summary>
    public string LicenseContractNo { get; set; }

    /// <summary>
    /// 合同时间
    /// </summary>
    public DateTime? LicenseContractDate { get; set; }

    /// <summary>
    /// 合同到期日
    /// </summary>
    public DateTime? LicenseExpiryDate { get; set; }

    /// <summary>
    /// 进口许可证
    /// </summary>
    public string LicenseImportNo { get; set; }

    // ===== 发票信息 =====

    /// <summary>
    /// 发票分类
    /// </summary>
    public string InvoiceType { get; set; }

    /// <summary>
    /// 发票号码
    /// </summary>
    public string InvoiceNo { get; set; }

    /// <summary>
    /// 开具日期
    /// </summary>
    public DateTime? InvoiceDate { get; set; }

    /// <summary>
    /// 付款方式
    /// </summary>
    public string PaymentMethod { get; set; }

    /// <summary>
    /// 发票分类代码
    /// </summary>
    public string InvoiceTypeCode { get; set; }

    /// <summary>
    /// 发票金额
    /// </summary>
    public decimal? InvoiceAmount { get; set; }

    /// <summary>
    /// 交货条款
    /// </summary>
    public string DeliveryTerms { get; set; }

    /// <summary>
    /// 货币单位
    /// </summary>
    public string Currency { get; set; }

    /// <summary>
    /// 价值申报的分类代码
    /// </summary>
    public string CustomsValueCode { get; set; }

    /// <summary>
    /// 运费
    /// </summary>
    public decimal? Freight { get; set; }

    /// <summary>
    /// 保险费
    /// </summary>
    public decimal? Insurance { get; set; }

    // ===== 税务信息 =====

    /// <summary>
    /// 确定纳税截止日期代码
    /// </summary>
    public string TaxDueDateCode { get; set; }

    /// <summary>
    /// 报关单号
    /// </summary>
    public string DeclarationNo { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string Supplier { get; set; }

}
