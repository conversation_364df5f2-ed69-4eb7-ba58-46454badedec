﻿using System.Text;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using YwAdmin.Api.Host.Options;
using YwAdmin.Core;
using Volo.Abp;
using Volo.Abp.AspNetCore.ExceptionHandling;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.ApiExploring;
using Volo.Abp.AspNetCore.Mvc.Conventions;
using Volo.Abp.Autofac;
using Volo.Abp.Json;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;
using Volo.Abp.Localization;
using YwAdmin.Application;
using YwAdmin.Production;
using System.IdentityModel.Tokens.Jwt;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Purchase;

namespace YwAdmin.Api.Host
{
    [DependsOn(
        typeof(AbpSwashbuckleModule),
        typeof(AbpAutofacModule),
        typeof(AdminCoreModule),
        typeof(AdminApplicationModule),
        typeof(AdminProductionModule),
        typeof(AdminPurchaseModule)
        )]
    public class AdminHostModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();
            var hostingEnvironment = context.Services.GetHostingEnvironment();
            //Configure<AbpLocalizationOptions>(options =>
            //{
            //    options.Languages.Add(new LanguageInfo("en", "en", "English"));
            //    options.Languages.Add(new LanguageInfo("zh-Hans", "zh-Hans", "简体中文"));
            //});
            ConfigureControllers(context, hostingEnvironment);
            ConfigureAuthorizationServices(context, configuration);
            ConfigureSwaggerServices(context);
            ConfigureCors(context, configuration);
        }

        private void ConfigureControllers(ServiceConfigurationContext context, IWebHostEnvironment hostEnvironment)
        {
            // HTTP 状态代码映射（配合 PersistdValidateException 返回 400）
            context.Services.AddSingleton<IHttpExceptionStatusCodeFinder, AdminHttpExceptionStatusCodeFinder>();

            // 发送异常详情到客户端 true（发送）/ false（不发送）
            context.Services.Configure<AbpExceptionHandlingOptions>(options =>
            {
                options.SendExceptionsDetailsToClients = hostEnvironment.IsDevelopment();
            });
            // 路由生成规则
            context.Services.AddTransient<IConventionalRouteBuilder, AdminConventionalRouteBuilder>();

            // 解决 string 类型默认增加 require 的标记，详见官方文档
            context.Services.AddControllers(options =>
            {
                options.Filters.AddService<AdminAbpExceptionsFilter>();
                options.Filters.Add<AdminRequestLogFilter>();
                options.Filters.Add<AdminUnitOfWorkFilter>();
                options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
            });

            // 时间格式化
            Configure<AbpJsonOptions>(options => options.OutputDateTimeFormat = "yyyy-MM-dd HH:mm:ss");
        }

        private void ConfigureAuthorizationServices(ServiceConfigurationContext context, IConfiguration configuration)
        {
            context.Services.AddSingleton<IAuthorizationHandler, AuthorizationHandler>();
            context.Services.AddSingleton<IAuthorizationMiddlewareResultHandler, AuthorizationMiddlewareResultHandler>();

            context.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
                {
                    // 获取JWT配置选项
                    var jwtOptions = configuration.GetRequiredSection("JwtOptions").Get<YwAdmin.Multiplex.AdminUser.JwtOptions>() ?? new YwAdmin.Multiplex.AdminUser.JwtOptions();

                    // 获取密钥
                    var key = Encoding.UTF8.GetBytes(jwtOptions.SecretKey);

                    options.RequireHttpsMetadata = false;
                    options.SaveToken = true;
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        // 验证发行者
                        ValidateIssuer = true,
                        ValidIssuer = jwtOptions.Issuer,

                        // 验证接收者
                        ValidateAudience = true,
                        ValidAudience = jwtOptions.Audience,

                        // 验证Token有效期
                        ValidateLifetime = true,

                        // 验证签名密钥
                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(key),

                        // 设置时钟偏移量，减少因服务器时间差异导致的验证问题
                        ClockSkew = TimeSpan.FromSeconds(10)
                    };

                    // 配置JWT事件处理
                    options.Events = new JwtBearerEvents
                    {
                        // 接收消息时的处理
                        OnMessageReceived = context =>
                        {
                            // 支持通过查询参数传递Token（用于SignalR等场景）
                            var accessToken = context.Request.Query["access_token"];
                            var path = context.HttpContext.Request.Path;
                            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/signalr-hubs"))
                            {
                                context.Token = accessToken;
                            }
                            return Task.CompletedTask;
                        },

                        // Token验证成功时的处理
                        OnTokenValidated = async context =>
                        {
                            // 获取Token验证服务
                            var tokenService = context.HttpContext.RequestServices.GetRequiredService<IAdminToken>();

                            // 获取当前Token
                            var token = context.SecurityToken as JwtSecurityToken;
                            if (token != null)
                            {
                                // 检查Token是否在黑名单中
                                var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
                                if (!await tokenService.ValidateTokenAsync(tokenString))
                                {
                                    context.Fail("Token has been revoked");
                                }
                            }
                        }
                    };
                });

            // 解决 POST 请求返回 400 错误
            Configure<AbpAntiForgeryOptions>(options =>
            {
                options.AutoValidate = false;
            });
        }

        private void ConfigureSwaggerServices(ServiceConfigurationContext context)
        {
            context.Services.AddAbpSwaggerGen(options =>
            {
                options.SwaggerDoc("system", new OpenApiInfo { Title = "系统管理" });
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "YwAdmin.Application.xml"), true);
                options.SwaggerDoc("production", new OpenApiInfo { Title = "生产管理" });
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "YwAdmin.Production.xml"), true);
                options.SwaggerDoc("purchase", new OpenApiInfo { Title = "采购管理" });
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "YwAdmin.Purchase.xml"), true);
                options.CustomSchemaIds(type => type.FullName); // 使用完整类型名称
                options.HideAbpEndpoints();
                options.SchemaFilter<HideAbpSchemaFilter>();
                options.MapType<DateTime>(() => new OpenApiSchema { Type = "string" });

                // Swagger 授权方案定义
                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme.",
                    BearerFormat = "JWT",
                    Scheme = "bearer",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                });

                // 加载授权方案
                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Id = "Bearer",
                                Type = ReferenceType.SecurityScheme
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });
            Configure<AbpRemoteServiceApiDescriptionProviderOptions>(options =>
            {
                options.SupportedResponseTypes.Clear();
            });
        }

        private void ConfigureCors(ServiceConfigurationContext context, IConfiguration configuration)
        {
            context.Services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    builder
                        .WithOrigins(configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.RemovePostFix("/"))
                            .ToArray() ?? [])
                        .WithAbpExposedHeaders()
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
            });

            // 解决 'The cookie 'XSRF-TOKEN' has set 'SameSite=None' and must also set 'Secure' 警告
            context.Services.Configure<CookiePolicyOptions>(options =>
            {
                options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                options.OnAppendCookie = cookieContext =>
                {
                    if (cookieContext.CookieOptions.SameSite == SameSiteMode.None)
                    {
                        if (cookieContext.Context.Request.Scheme != "https")
                        {
                            cookieContext.CookieOptions.SameSite = SameSiteMode.Unspecified;
                        }
                    }
                };
                options.OnDeleteCookie = cookieContext =>
                {
                    if (cookieContext.CookieOptions.SameSite == SameSiteMode.None)
                    {
                        if (cookieContext.Context.Request.Scheme != "https")
                        {
                            cookieContext.CookieOptions.SameSite = SameSiteMode.Unspecified;
                        }
                    }
                };
            });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();
            var env = context.GetEnvironment();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(options =>
                {
                    options.SwaggerEndpoint("/swagger/system/swagger.json", "系统管理");
                    options.SwaggerEndpoint("/swagger/production/swagger.json", "生产管理");
                    options.SwaggerEndpoint("/swagger/purchase/swagger.json", "采购管理");
                    var responseInterceptor = @" (res) => {
                        const token = res.headers.accesstoken;
                        if(token){localStorage.setItem('token', token);}
                        return res;
                    }";
                    var requestInterceptor = @" (req) => {
                        req.headers.Authorization = 'Bearer ' + localStorage.getItem('token');
                        return req;
                    }";
                    options.UseResponseInterceptor(Regex.Replace(responseInterceptor, @"\s+", " "));
                    options.UseRequestInterceptor(Regex.Replace(requestInterceptor, @"\s+", " "));
                });
            }

            app.UseCookiePolicy();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseConfiguredEndpoints(options =>
            {
                options.MapControllers().RequireAuthorization();
            });
        }
    }
}